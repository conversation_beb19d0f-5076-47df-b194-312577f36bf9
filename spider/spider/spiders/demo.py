# example_spider.py
# 使用AioHTTP中间件的示例Spider

import scrapy
from scrapy.crawler import CrawlerProcess
import json


class ExampleSpider(scrapy.Spider):
    """示例爬虫，展示如何使用AioHTTP中间件"""

    name = 'example_aiohttp'

    # 自定义设置，覆盖项目设置
    custom_settings = {
        'DOWNLOADER_MIDDLEWARES': {
            # 禁用默认的HTTP中间件
            'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': None,
            'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware': None,
            # 启用AioHTTP中间件
            'scrapy_aiohttp_middleware.AioHttpDownloaderMiddleware': 543,
        },
        'CONCURRENT_REQUESTS': 16,
        'DOWNLOAD_TIMEOUT': 30,
        'COOKIES_ENABLED': True,
        'AIOHTTP_VERIFY_SSL': True,
    }

    def start_requests(self):
        """生成初始请求"""
        urls = [
            'http://httpbin.org/get',
            'http://httpbin.org/headers',
            'http://httpbin.org/user-agent',
            'http://httpbin.org/cookies/set?test=value',
            'http://httpbin.org/status/200',
        ]

        for url in urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={
                    'download_timeout': 20,  # 自定义超时
                }
            )

        # POST请求示例
        yield scrapy.Request(
            url='http://httpbin.org/post',
            method='POST',
            body=json.dumps({'key': 'value'}),
            headers={'Content-Type': 'application/json'},
            callback=self.parse_post
        )

        # 使用代理的示例
        yield scrapy.Request(
            url='http://httpbin.org/ip',
            callback=self.parse,
            meta={
                'proxy': 'http://proxy.example.com:8080',
                'proxy_auth': ('username', 'password'),  # 代理认证
            }
        )

    def parse(self, response):
        """解析响应"""
        self.logger.info(f'Response from {response.url}:')
        self.logger.info(f'Status: {response.status}')
        self.logger.info(f'Headers: {response.headers}')

        try:
            data = response.json()
            self.logger.info(f'JSON Data: {json.dumps(data, indent=2)}')
        except:
            self.logger.info(f'Body: {response.text[:200]}...')

        # 提取更多链接
        for link in response.css('a::attr(href)').getall():
            yield response.follow(link, callback=self.parse)

    def parse_post(self, response):
        """解析POST响应"""
        self.logger.info(f'POST Response: {response.json()}')


# test_middleware.py
# 中间件测试代码

import unittest
import asyncio
from unittest.mock import Mock, MagicMock, patch
from scrapy.http import Request, HtmlResponse
from scrapy_aiohttp_middleware import AioHttpDownloaderMiddleware


class TestAioHttpMiddleware(unittest.TestCase):
    """AioHTTP中间件单元测试"""

    def setUp(self):
        """测试初始化"""
        self.settings = {
            'CONCURRENT_REQUESTS': 16,
            'DOWNLOAD_TIMEOUT': 30,
            'COOKIES_ENABLED': True,
            'AIOHTTP_VERIFY_SSL': True,
        }

        self.crawler = Mock()
        self.crawler.settings = Mock()
        self.crawler.settings.getint = lambda k, d=None: self.settings.get(k, d)
        self.crawler.settings.getfloat = lambda k, d=None: self.settings.get(k, d)
        self.crawler.settings.getbool = lambda k, d=None: self.settings.get(k, d)

        self.middleware = AioHttpDownloaderMiddleware(self.crawler.settings, self.crawler)
        self.spider = Mock(name='test_spider')

    def test_initialization(self):
        """测试中间件初始化"""
        self.assertEqual(self.middleware.concurrent_requests, 16)
        self.assertEqual(self.middleware.download_timeout, 30)
        self.assertIsNotNone(self.middleware.cookie_jar)
        self.assertIsNotNone(self.middleware.ssl_context)

    def test_from_crawler(self):
        """测试from_crawler类方法"""
        middleware = AioHttpDownloaderMiddleware.from_crawler(self.crawler)
        self.assertIsInstance(middleware, AioHttpDownloaderMiddleware)

    @patch('aiohttp.ClientSession')
    async def test_create_session(self, mock_session):
        """测试session创建"""
        mock_session.return_value.closed = False

        session = await self.middleware._create_session()
        self.assertIsNotNone(session)
        mock_session.assert_called_once()

    def test_prepare_request_kwargs(self):
        """测试请求参数准备"""
        request = Request(
            url='http://example.com',
            headers={'User-Agent': 'Test'},
            body=b'test body',
            meta={
                'proxy': 'http://proxy.com:8080',
                'download_timeout': 10,
            }
        )

        kwargs = self.middleware._prepare_request_kwargs(request)

        self.assertIn('headers', kwargs)
        self.assertIn('data', kwargs)
        self.assertIn('proxy', kwargs)
        self.assertEqual(kwargs['proxy'], 'http://proxy.com:8080')
        self.assertEqual(kwargs['timeout'].total, 10)

    def test_build_response(self):
        """测试响应构建"""
        request = Request('http://example.com')

        # 模拟aiohttp响应
        mock_response = Mock()
        mock_response.url = 'http://example.com'
        mock_response.status = 200
        mock_response.charset = 'utf-8'
        mock_response.headers = {'Content-Type': 'text/html'}

        content = b'<html><body>Test</body></html>'

        response = self.middleware._build_response(request, mock_response, content)

        self.assertIsInstance(response, HtmlResponse)
        self.assertEqual(response.status, 200)
        self.assertEqual(response.body, content)
        self.assertEqual(response.encoding, 'utf-8')

    def test_process_request_skip(self):
        """测试跳过处理的请求"""
        request = Request('http://example.com', meta={'aiohttp_skip': True})
        result = self.middleware.process_request(request, self.spider)
        self.assertIsNone(result)

    async def test_handle_download_delay(self):
        """测试下载延迟处理"""
        self.middleware.download_delay = 1.0
        request1 = Request('http://example.com/page1')
        request2 = Request('http://example.com/page2')

        start = asyncio.get_event_loop().time()
        await self.middleware._handle_download_delay(request1)
        await self.middleware._handle_download_delay(request2)
        end = asyncio.get_event_loop().time()

        # 第二个请求应该有延迟
        self.assertGreaterEqual(end - start, 1.0)


# run_example.py
# 运行示例的脚本

if __name__ == '__main__':
    # 配置日志
    import logging

    logging.getLogger('scrapy').setLevel(logging.DEBUG)

    # 创建并运行爬虫
    process = CrawlerProcess({
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'ROBOTSTXT_OBEY': False,
        'DOWNLOAD_DELAY': 0.5,
        # 确保中间件被加载
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy_aiohttp_middleware.AioHttpDownloaderMiddleware': 543,
        }
    })

    process.crawl(ExampleSpider)
    process.start()


# advanced_usage.py
# 高级用法示例

class AdvancedSpider(scrapy.Spider):
    """展示高级功能的爬虫"""

    name = 'advanced_aiohttp'

    custom_settings = {
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy_aiohttp_middleware.AioHttpAdvancedMiddleware': 543,
        },
        'AIOHTTP_RATE_LIMIT': 5,  # 每秒5个请求
        'AIOHTTP_CACHE_ENABLED': True,  # 启用缓存
        'AIOHTTP_RETRY_TIMES': 3,  # 重试3次
    }

    def start_requests(self):
        """生成请求，展示高级功能"""

        # 1. 自定义超时的请求
        yield scrapy.Request(
            url='http://httpbin.org/delay/5',
            meta={'download_timeout': 10},
            callback=self.parse
        )

        # 2. 禁用SSL验证的请求（用于测试环境）
        yield scrapy.Request(
            url='https://self-signed.badssl.com/',
            meta={'verify_ssl': False},
            callback=self.parse
        )

        # 3. 自定义编码的请求
        yield scrapy.Request(
            url='http://httpbin.org/encoding/utf8',
            meta={'encoding': 'utf-8'},
            callback=self.parse
        )

        # 4. 处理重定向
        yield scrapy.Request(
            url='http://httpbin.org/redirect/3',
            meta={'handle_httpstatus_list': [301, 302]},
            callback=self.parse
        )

        # 5. 发送JSON数据
        yield scrapy.Request(
            url='http://httpbin.org/post',
            method='POST',
            headers={'Content-Type': 'application/json'},
            body=json.dumps({
                'name': 'test',
                'data': {'key': 'value'}
            }),
            callback=self.parse
        )

        # 6. 使用会话保持（cookies）
        yield scrapy.Request(
            url='http://httpbin.org/cookies/set?session=12345',
            callback=self.parse_cookies,
            dont_filter=True
        )

    def parse(self, response):
        """通用解析方法"""
        self.logger.info(f'Got response from {response.url}')
        self.logger.info(f'Status: {response.status}')

        if response.headers.get('Content-Type', b'').startswith(b'application/json'):
            data = response.json()
            yield {
                'url': response.url,
                'data': data
            }

    def parse_cookies(self, response):
        """解析cookie响应"""
        # 发送另一个请求以验证cookie
        yield scrapy.Request(
            url='http://httpbin.org/cookies',
            callback=self.verify_cookies,
            dont_filter=True
        )

    def verify_cookies(self, response):
        """验证cookies是否保持"""
        data = response.json()
        self.logger.info(f'Cookies: {data.get("cookies", {})}')


# 性能测试脚本
# performance_test.py

import time
import statistics
from scrapy.crawler import CrawlerRunner
from twisted.internet import reactor, defer


class PerformanceTestSpider(scrapy.Spider):
    """性能测试爬虫"""

    name = 'performance_test'
    request_times = []

    custom_settings = {
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy_aiohttp_middleware.AioHttpDownloaderMiddleware': 543,
        },
        'CONCURRENT_REQUESTS': 100,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 50,
        'DOWNLOAD_TIMEOUT': 10,
        'RETRY_ENABLED': False,
        'COOKIES_ENABLED': False,
    }

    def start_requests(self):
        """生成大量请求进行性能测试"""
        base_url = 'http://httpbin.org/anything?page={}'

        for i in range(1000):  # 1000个请求
            yield scrapy.Request(
                url=base_url.format(i),
                callback=self.parse,
                meta={'request_start': time.time()},
                dont_filter=True
            )

    def parse(self, response):
        """记录响应时间"""
        request_time = time.time() - response.meta['request_start']
        self.request_times.append(request_time)

        if len(self.request_times) % 100 == 0:
            self.logger.info(f'Processed {len(self.request_times)} requests')

    @classmethod
    def get_stats(cls):
        """获取性能统计"""
        if not cls.request_times:
            return None

        return {
            'total_requests': len(cls.request_times),
            'average_time': statistics.mean(cls.request_times),
            'median_time': statistics.median(cls.request_times),
            'min_time': min(cls.request_times),
            'max_time': max(cls.request_times),
            'std_dev': statistics.stdev(cls.request_times) if len(cls.request_times) > 1 else 0
        }


@defer.inlineCallbacks
def run_performance_test():
    """运行性能测试"""
    runner = CrawlerRunner()

    start_time = time.time()
    yield runner.crawl(PerformanceTestSpider)
    end_time = time.time()

    stats = PerformanceTestSpider.get_stats()
    if stats:
        print(f"\n性能测试结果:")
        print(f"总请求数: {stats['total_requests']}")
        print(f"总时间: {end_time - start_time:.2f}秒")
        print(f"平均响应时间: {stats['average_time']:.3f}秒")
        print(f"中位数响应时间: {stats['median_time']:.3f}秒")
        print(f"最快响应: {stats['min_time']:.3f}秒")
        print(f"最慢响应: {stats['max_time']:.3f}秒")
        print(f"标准差: {stats['std_dev']:.3f}")
        print(f"QPS: {stats['total_requests'] / (end_time - start_time):.2f}")

    reactor.stop()


if __name__ == '__main__':
    run_performance_test()
    reactor.run()