import ujson
from scrapy_redis.spiders import <PERSON><PERSON><PERSON><PERSON><PERSON>, bytes_to_str
from scrapy.http import Request, FormRequest
from spider.spider.utils.base import ScheduledRequest


class AntCodeRedisSpider(RedisSpider):

    def get_callback(self, callback):
        """
        :param callback:
        :return: 继承时重写该函数，返回一个元组(func, bool)
        """
        return None, False

    def make_request_from_data(self, data):

        scheduled = ScheduledRequest(
            **ujson.loads(
                bytes_to_str(data, self.redis_encoding)
            )
        )

        callback, dont_filter = self.get_callback(scheduled.callback)
        if not callable(callback):
            raise OSError(f"{scheduled.callback}没有指定回调函数")

        params = {
            "url": scheduled.url,
            'method': scheduled.method,
            "meta": scheduled.meta,
            "callback": callback,
            "dont_filter": dont_filter,
        }

        if 'browser' in scheduled.meta:
            pass
        elif 'curl_cffi' in scheduled.meta:
            pass
        else:
            if scheduled.method == "POST":
                return FormRequest(formdata=scheduled.body, **params)
            else:
                return Request(**params)
