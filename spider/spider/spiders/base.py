"""动态任务爬虫基类模块"""

import re
import copy
import ujson
from scrapy_redis.spiders import RedisSpider
from scrapy import Request
from urllib.parse import urljoin


class DynamicTaskSpider(RedisSpider):
    """
    动态任务爬虫基类

    核心功能：任务解析、回调路由、数据提取、代理支持
    """
    
    def make_requests_from_url(self, url):
        """解析Redis任务数据，支持JSON格式"""
        try:
            # 解析任务数据
            task_data = ujson.loads(url) if isinstance(url, str) and url.startswith('{') else {"url": url, "callback": "parse_list"}

            # 准备meta数据
            meta = task_data.get('meta', {})
            meta['task_meta'] = task_data

            # 处理代理设置
            if proxy := meta.get('proxy'):
                meta['proxy'] = proxy
                self.logger.info(f"Using proxy: {proxy}")

            return Request(
                url=task_data.get('url'),
                method=task_data.get('method', 'GET'),
                headers=copy.deepcopy(task_data.get('headers', {})),
                cookies=copy.deepcopy(task_data.get('cookies', {})),
                body=task_data.get('body', ''),
                dont_filter=task_data.get('dont_filter', False),
                meta=meta,
                callback=self.parse
            )
        except Exception as e:
            self.logger.error(f"Task parsing error: {e}")
            return Request(url=url, callback=self.parse)

    def get_callback(self, callback):
        """获取回调方法和去重设置"""
        callback_mapping = {
            'parse_list': (self.parse_list, True),
            'parse_detail': (self.parse_detail, True),
            'list': (self.parse_list, True),
            'detail': (self.parse_detail, True),
        }
        return callback_mapping.get(callback)

    def parse(self, response):
        """主解析方法，根据callback字段路由到处理函数"""
        try:
            task_meta = response.meta.get('task_meta', {})
            callback_name = task_meta.get('callback', 'parse_list')

            self.logger.info(f"Processing {response.url} with {callback_name}")

            if callback_info := self.get_callback(callback_name):
                callback_method, dont_filter = callback_info
                response.meta['dont_filter'] = dont_filter
                yield from callback_method(response)
            else:
                self.logger.warning(f"Unknown callback: {callback_name}")
                yield from self.parse_list(response)
        except Exception as e:
            self.logger.error(f"Parse error {response.url}: {e}")

    def extract_data_by_rules(self, response, rules):
        """根据规则提取数据，支持xpath、css、regex"""
        results = []

        for rule in rules:
            if not (expr := rule.get('expr', '')):
                continue

            rule_type = rule.get('type', '').lower()
            desc = rule.get('desc', '')

            try:
                # 数据提取
                if rule_type == 'xpath':
                    extracted_data = response.xpath(expr).getall()
                elif rule_type == 'css':
                    extracted_data = response.css(expr).getall()
                elif rule_type == 'regex':
                    matches = re.findall(expr, response.text)
                    extracted_data = [m[0] if isinstance(m, tuple) else m for m in matches] if matches else []
                else:
                    self.logger.warning(f"Unsupported rule type: {rule_type}")
                    continue

                # 数据清理
                cleaned_data = [item.strip() for item in extracted_data if isinstance(item, str) and item.strip()]

                if cleaned_data:
                    results.append({
                        'type': rule_type,
                        'expr': expr,
                        'desc': desc,
                        'data': cleaned_data
                    })
                    self.logger.info(f"Rule '{desc}' extracted {len(cleaned_data)} items")

            except Exception as e:
                self.logger.error(f"Rule error {rule}: {e}")

        return results

    def create_follow_request(self, url, base_url, task_meta, callback_name='parse'):
        """创建跟进请求"""
        meta = {'task_meta': task_meta}

        # 处理代理设置
        if proxy := task_meta.get('meta', {}).get('proxy'):
            meta['proxy'] = proxy
            self.logger.info(f"Follow request using proxy: {proxy}")

        # 获取去重设置
        target_callback = task_meta.get('callback', 'parse_list')
        dont_filter = self.get_callback(target_callback)[1] if self.get_callback(target_callback) else False

        return Request(
            url=urljoin(base_url, url),
            method=task_meta.get('method', 'GET'),
            headers=copy.deepcopy(task_meta.get('headers', {})),
            cookies=copy.deepcopy(task_meta.get('cookies', {})),
            body=task_meta.get('body', ''),
            meta=meta,
            callback=self.parse,
            dont_filter=dont_filter
        )

    def parse_list(self, response):
        """列表页解析方法 - 子类必须重写"""
        raise NotImplementedError("Subclasses must implement parse_list method")

    def parse_detail(self, response):
        """详情页解析方法 - 子类必须重写"""
        raise NotImplementedError("Subclasses must implement parse_detail method")

    def closed(self, reason):
        """爬虫关闭清理"""
        self.logger.info(f"Spider closed: {reason}")
        super().closed(reason)
