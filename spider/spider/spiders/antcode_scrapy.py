"""
AntCode Scrapy 爬虫实现

基于 DynamicTaskSpider 基类的具体业务实现，
专注于列表页和详情页的解析逻辑。
"""

import copy
from .base import DynamicTaskSpider


class AntcodeScrapySpider(DynamicTaskSpider):
    """
    AntCode 爬虫实现类

    继承自 DynamicTaskSpider，专注于具体的业务逻辑：
    1. 列表页解析和跟进
    2. 详情页数据提取
    3. 智能任务生成
    """
    name = "antcode_scrapy"
    allowed_domains = []  # 动态设置，不限制域名
    redis_key = 'AntcodeScrapySpider:start_urls'





    def parse_list(self, response):
        """
        处理列表页 - 实现具体的业务逻辑
        """
        task_meta = response.meta.get('task_meta', {})
        meta_info = task_meta.get('meta', {})
        rules = meta_info.get('rules', [])
        list_id = meta_info.get('list_id', '')
        extra = meta_info.get('extra', {})

        self.logger.info(f"Parsing list page: {response.url}")

        # 应用提取规则
        extraction_results = self.extract_data_by_rules(response, rules)

        # 处理提取结果，生成跟进任务
        for result in extraction_results:
            rule_desc = result.get('desc', '')
            extracted_data = result.get('data', [])

            # 根据规则描述判断数据类型并生成相应任务
            if self._is_detail_rule(rule_desc):
                yield from self._create_detail_tasks(extracted_data, response, task_meta, meta_info)
            elif self._is_pagination_rule(rule_desc):
                yield from self._create_pagination_tasks(extracted_data, response, task_meta)

        # 返回列表页数据项
        yield {
            'page_type': 'list',
            'url': response.url,
            'list_id': list_id,
            'extraction_results': extraction_results,
            'extra': extra,
            'timestamp': response.meta.get('download_timestamp')
        }


    def parse_detail(self, response):
        """
        处理详情页 - 实现具体的业务逻辑
        """
        task_meta = response.meta.get('task_meta', {})
        meta_info = task_meta.get('meta', {})
        rules = meta_info.get('rules', [])
        list_id = meta_info.get('list_id', '')
        parent_url = meta_info.get('parent_url', '')
        extra = meta_info.get('extra', {})

        self.logger.info(f"Parsing detail page: {response.url}")

        # 应用提取规则
        extraction_results = self.extract_data_by_rules(response, rules)

        # 构建详情页数据
        detail_data = self._build_detail_data(response, extraction_results, meta_info)

        yield detail_data
