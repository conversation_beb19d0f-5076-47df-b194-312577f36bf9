import scrapy
from AntCodeRedisSpider import AntCodeRedisSpider

class AntcodeScrapyNewSpider(AntCodeRedisSpider):
    name = "antcode_scrapy_new"
    allowed_domains = []  # 动态设置，不限制域名
    redis_key = 'AntcodeScrapySpider:start_urls'

    # (可选) 自定义 settings，覆盖 settings.py 中的全局配置
    # custom_settings = {
    #     'CONCURRENT_REQUESTS_PER_DOMAIN': 8,
    #     'LOG_LEVEL': 'DEBUG'
    # }

    def parse_list(self, response):



        pass

    def parse_detail(self, response):


        pass
