# scrapy_aiohttp_middleware.py

import asyncio
import aiohttp
from aiohttp import TCPConnector, ClientTimeout
from scrapy import signals
from scrapy.http import HtmlResponse, Request
from scrapy.downloadermiddlewares.retry import RetryMiddleware
from scrapy.utils.defer import deferred_from_coro
from scrapy.exceptions import IgnoreRequest, NotConfigured
from scrapy.utils.python import to_bytes, to_unicode
from twisted.internet import defer
import certifi
import ssl
from typing import Optional, Dict, Any, Union
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class AioHttpDownloaderMiddleware:
    """
    Scrapy下载中间件，使用aiohttp替换默认的Twisted请求方式
    """

    def __init__(self, settings, crawler):
        self.crawler = crawler
        self.settings = settings

        # 从settings中读取配置
        self.concurrent_requests = settings.getint('CONCURRENT_REQUESTS', 16)
        self.concurrent_requests_per_domain = settings.getint('CONCURRENT_REQUESTS_PER_DOMAIN', 8)
        self.download_timeout = settings.getfloat('DOWNLOAD_TIMEOUT', 180)
        self.download_delay = settings.getfloat('DOWNLOAD_DELAY', 0)

        # aiohttp session配置
        self.connector_args = {
            'limit': self.concurrent_requests,
            'limit_per_host': self.concurrent_requests_per_domain,
            'ttl_dns_cache': 300,
            'enable_cleanup_closed': True,
            'force_close': True,
        }

        # SSL配置
        self.ssl_context = self._create_ssl_context()

        # Cookie jar
        self.cookie_jar = aiohttp.CookieJar(unsafe=True) if settings.getbool('COOKIES_ENABLED', True) else None

        # 会话和事件循环
        self.session = None
        self.loop = None

        # 域名延迟控制
        self.domain_delays = {}

    @classmethod
    def from_crawler(cls, crawler):
        """从crawler创建中间件实例"""
        try:
            return cls(crawler.settings, crawler)
        except Exception as e:
            raise NotConfigured(f'AioHttpDownloaderMiddleware configuration error: {e}')

    def _create_ssl_context(self):
        """创建SSL上下文"""
        ssl_context = ssl.create_default_context(cafile=certifi.where())

        # 根据settings配置SSL验证
        if not self.settings.getbool('AIOHTTP_VERIFY_SSL', True):
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

        return ssl_context

    async def _create_session(self):
        """创建aiohttp session"""
        if not self.session or self.session.closed:
            timeout = ClientTimeout(total=self.download_timeout)
            connector = TCPConnector(**self.connector_args, ssl=self.ssl_context)

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                cookie_jar=self.cookie_jar,
                trust_env=True,  # 支持环境变量中的代理设置
            )

        return self.session

    def spider_opened(self, spider):
        """蜘蛛开启时初始化"""
        logger.info(f'Spider opened: {spider.name} with AioHTTP middleware')
        if not self.loop:
            try:
                self.loop = asyncio.get_event_loop()
            except RuntimeError:
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)

    def spider_closed(self, spider):
        """蜘蛛关闭时清理资源"""
        logger.info(f'Spider closed: {spider.name}, cleaning up AioHTTP session')
        if self.session and not self.session.closed:
            self.loop.run_until_complete(self.session.close())

    def process_request(self, request: Request, spider):
        """处理请求，返回Response或Deferred"""
        # 检查是否应该忽略此请求
        if request.meta.get('aiohttp_skip', False):
            return None

        # 返回Deferred对象，Scrapy会等待异步操作完成
        return deferred_from_coro(self._download(request, spider))

    async def _download(self, request: Request, spider):
        """异步下载请求"""
        await self._create_session()

        # 处理域名延迟
        await self._handle_download_delay(request)

        # 准备请求参数
        kwargs = self._prepare_request_kwargs(request)

        try:
            # 发送请求
            async with self.session.request(
                    method=request.method,
                    url=request.url,
                    **kwargs
            ) as response:
                # 读取响应内容
                content = await response.read()

                # 构建Scrapy Response对象
                return self._build_response(request, response, content)

        except asyncio.TimeoutError:
            logger.error(f'Timeout downloading {request.url}')
            raise
        except aiohttp.ClientError as e:
            logger.error(f'Error downloading {request.url}: {e}')
            raise
        except Exception as e:
            logger.error(f'Unexpected error downloading {request.url}: {e}')
            raise

    def _prepare_request_kwargs(self, request: Request) -> Dict[str, Any]:
        """准备aiohttp请求参数"""
        kwargs = {}

        # 请求头
        headers = {}
        for key, value in request.headers.items():
            headers[to_unicode(key)] = [to_unicode(v) for v in value]
        kwargs['headers'] = headers

        # 请求体
        if request.body:
            kwargs['data'] = request.body

        # 代理设置
        proxy = request.meta.get('proxy')
        if proxy:
            kwargs['proxy'] = proxy
            # 代理认证
            proxy_auth = request.meta.get('proxy_auth')
            if proxy_auth:
                kwargs['proxy_auth'] = aiohttp.BasicAuth(proxy_auth[0], proxy_auth[1])

        # 超时设置（可以被request.meta覆盖）
        timeout = request.meta.get('download_timeout', self.download_timeout)
        kwargs['timeout'] = ClientTimeout(total=timeout)

        # SSL验证（可以被request.meta覆盖）
        if 'verify_ssl' in request.meta:
            if not request.meta['verify_ssl']:
                kwargs['ssl'] = False
            else:
                kwargs['ssl'] = self.ssl_context

        # 允许重定向
        kwargs['allow_redirects'] = request.meta.get('handle_httpstatus_list', []) == []

        # 编码
        if 'encoding' in request.meta:
            kwargs['encoding'] = request.meta['encoding']

        return kwargs

    def _build_response(self, request: Request, aiohttp_response, content: bytes):
        """构建Scrapy Response对象"""
        # 获取编码
        encoding = aiohttp_response.charset or 'utf-8'

        # 构建响应头
        headers = {}
        for key, value in aiohttp_response.headers.items():
            headers[to_bytes(key)] = [to_bytes(value)]

        # 创建Response对象
        response_cls = HtmlResponse

        return response_cls(
            url=str(aiohttp_response.url),
            status=aiohttp_response.status,
            headers=headers,
            body=content,
            encoding=encoding,
            request=request
        )

    async def _handle_download_delay(self, request: Request):
        """处理下载延迟"""
        if self.download_delay <= 0:
            return

        domain = urlparse(request.url).netloc
        now = asyncio.get_event_loop().time()

        if domain in self.domain_delays:
            delay = self.domain_delays[domain] + self.download_delay - now
            if delay > 0:
                await asyncio.sleep(delay)

        self.domain_delays[domain] = now

    def process_response(self, request: Request, response, spider):
        """处理响应（可选的后处理）"""
        # 这里可以添加响应后处理逻辑
        return response

    def process_exception(self, request: Request, exception, spider):
        """处理异常"""
        # 记录异常
        logger.error(f'Exception processing {request.url}: {exception}')

        # 可以根据异常类型决定是否重试
        if isinstance(exception, (asyncio.TimeoutError, aiohttp.ClientError)):
            # 让RetryMiddleware处理重试逻辑
            return None

        # 其他异常直接传递
        return None


# 扩展的中间件，支持更多高级功能
class AioHttpAdvancedMiddleware(AioHttpDownloaderMiddleware):
    """
    高级版本的aiohttp中间件，支持更多功能：
    - 连接池管理
    - 请求优先级
    - 自定义重试策略
    - 响应缓存
    - 请求限流
    """

    def __init__(self, settings, crawler):
        super().__init__(settings, crawler)

        # 请求限流
        self.rate_limit = settings.getfloat('AIOHTTP_RATE_LIMIT', 0)  # 每秒请求数
        self.rate_limiter = None
        if self.rate_limit > 0:
            self.rate_limiter = asyncio.Semaphore(int(self.rate_limit))

        # 缓存设置
        self.cache_enabled = settings.getbool('AIOHTTP_CACHE_ENABLED', False)
        self.cache = {} if self.cache_enabled else None

        # 自定义重试次数
        self.retry_times = settings.getint('AIOHTTP_RETRY_TIMES', 2)

    async def _download(self, request: Request, spider):
        """增强的下载方法，支持限流和缓存"""
        # 检查缓存
        if self.cache_enabled and request.method == 'GET':
            cache_key = request.url
            if cache_key in self.cache:
                logger.debug(f'Cache hit for {request.url}')
                return self.cache[cache_key]

        # 限流控制
        if self.rate_limiter:
            async with self.rate_limiter:
                response = await super()._download(request, spider)
        else:
            response = await super()._download(request, spider)

        # 缓存响应
        if self.cache_enabled and request.method == 'GET' and response.status == 200:
            self.cache[request.url] = response

        return response

    def _prepare_request_kwargs(self, request: Request) -> Dict[str, Any]:
        """增强的请求参数准备"""
        kwargs = super()._prepare_request_kwargs(request)

        # 添加更多自定义参数
        # 压缩支持
        if request.meta.get('compress', True):
            kwargs['compress'] = True

        # 自定义连接限制
        if 'max_connections' in request.meta:
            # 这需要动态调整connector，这里仅作示例
            pass

        return kwargs


# settings.py 配置示例
"""
# 启用AioHTTP中间件
DOWNLOADER_MIDDLEWARES = {
    'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': None,  # 禁用默认的
    'your_project.middlewares.AioHttpDownloaderMiddleware': 543,
}

# AioHTTP中间件配置
AIOHTTP_VERIFY_SSL = True  # SSL验证
AIOHTTP_RATE_LIMIT = 10  # 每秒最多10个请求
AIOHTTP_CACHE_ENABLED = False  # 是否启用缓存
AIOHTTP_RETRY_TIMES = 3  # 重试次数

# 其他相关配置
CONCURRENT_REQUESTS = 32
CONCURRENT_REQUESTS_PER_DOMAIN = 16
DOWNLOAD_TIMEOUT = 30
DOWNLOAD_DELAY = 0.5
COOKIES_ENABLED = True
"""